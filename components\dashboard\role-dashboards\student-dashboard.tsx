'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/card'
import { StatsGrid } from '@/components/stats-grid'
import {
  GraduationCap,
  Calendar,
  CheckCircle,
  ArrowUpRight,
  BookOpen,
  Loader2,
  ClipboardCheck,
  Award,
  CreditCard,
  Clock,
  TrendingUp,
  User
} from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'
import { useDashboardStore } from '@/lib/stores/dashboard-store'
import { Button } from '@/components/button'
import Link from 'next/link'
import { useSession } from 'next-auth/react'

interface StudentDashboardStats {
  currentLevel: string
  nextLevel: string
  attendanceRate: { rate: number; trend: number }
  averageScore: { score: number; trend: number }
  paymentStatus: { status: string; nextDue: string; amount: number }
  upcomingClasses: Array<{ subject: string; teacher: string; time: string; room: string }>
  recentGrades: Array<{ testName: string; score: number; date: string; passed: boolean }>
  progressToNext: { percentage: number; requirements: string[] }
}

export default function StudentDashboard() {
  const { data: session } = useSession()
  const { currentBranch } = useBranch()
  const { refreshData } = useDashboardStore()
  const [stats, setStats] = useState<StudentDashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchStudentStats()
    refreshData()
  }, [])

  const fetchStudentStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/dashboard/student-stats')
      if (!response.ok) {
        throw new Error('Failed to fetch student dashboard stats')
      }
      const data = await response.json()
      setStats(data)
      setError(null)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching student dashboard stats:', error)
      }
      setError('Failed to load student dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('uz-UZ', {
      style: 'currency',
      currency: 'UZS',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('UZS', 'UZS')
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'text-green-600'
      case 'PENDING': return 'text-orange-600'
      case 'OVERDUE': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchStudentStats}>Try Again</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 tracking-tight">
            <User className="inline h-8 w-8 mr-3 text-blue-600" />
            Welcome, {session?.user?.name}
          </h1>
          <p className="text-gray-600 mt-1">Your learning journey and progress overview.</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            className="shadow-sm"
            onClick={() => {
              fetchStudentStats()
              refreshData()
            }}
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            Refresh Data
          </Button>
          <Button className="shadow-sm" disabled>
            <Calendar className="h-4 w-4 mr-2" />
            My Schedule
          </Button>
        </div>
      </div>

      {/* Student Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Current Level</CardTitle>
              <div className="h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center">
                <GraduationCap className="h-5 w-5 text-blue-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-blue-700">{stats?.currentLevel || 'N/A'}</div>
            <div className="kpi-change mt-2 text-blue-600">
              <ArrowUpRight className="h-4 w-4" />
              <span>Next: {stats?.nextLevel || 'N/A'}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Attendance Rate</CardTitle>
              <div className="h-10 w-10 rounded-full bg-green-50 flex items-center justify-center">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-green-700">{stats?.attendanceRate.rate || 0}%</div>
            <div className={`kpi-change mt-2 ${(stats?.attendanceRate.trend ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(stats?.attendanceRate.trend ?? 0) >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <Clock className="h-4 w-4" />
              )}
              <span>{(stats?.attendanceRate.trend ?? 0) >= 0 ? 'Good' : 'Needs improvement'}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Average Score</CardTitle>
              <div className="h-10 w-10 rounded-full bg-purple-50 flex items-center justify-center">
                <Award className="h-5 w-5 text-purple-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-purple-700">{stats?.averageScore.score || 0}%</div>
            <div className={`kpi-change mt-2 ${(stats?.averageScore.trend ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(stats?.averageScore.trend ?? 0) >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <Clock className="h-4 w-4" />
              )}
              <span>{(stats?.averageScore.trend ?? 0) >= 0 ? 'Improving' : 'Needs focus'}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Payment Status</CardTitle>
              <div className="h-10 w-10 rounded-full bg-orange-50 flex items-center justify-center">
                <CreditCard className="h-5 w-5 text-orange-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className={`kpi-value ${getPaymentStatusColor(stats?.paymentStatus.status || '')}`}>
              {stats?.paymentStatus.status || 'N/A'}
            </div>
            <div className="kpi-change mt-2 text-orange-600">
              <CreditCard className="h-4 w-4" />
              <span>Next: {stats?.paymentStatus.nextDue || 'N/A'}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Student Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <CardTitle className="text-lg font-semibold text-gray-900">Quick Actions</CardTitle>
            <CardDescription>Student portal features</CardDescription>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-3">
              <Button variant="outline" className="w-full justify-start" disabled>
                <Calendar className="h-4 w-4 mr-2" />
                My Schedule
              </Button>
              <Button variant="outline" className="w-full justify-start" disabled>
                <ClipboardCheck className="h-4 w-4 mr-2" />
                My Attendance
              </Button>
              <Button variant="outline" className="w-full justify-start" disabled>
                <Award className="h-4 w-4 mr-2" />
                My Grades
              </Button>
              <Button variant="outline" className="w-full justify-start" disabled>
                <CreditCard className="h-4 w-4 mr-2" />
                Payment History
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Upcoming Classes</CardTitle>
                <CardDescription className="mt-1">Your schedule</CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="text-sm">
                <Calendar className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {stats?.upcomingClasses && stats.upcomingClasses.length > 0 ? (
                stats.upcomingClasses.slice(0, 3).map((classItem, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div>
                      <p className="font-medium text-gray-900">{classItem.subject}</p>
                      <p className="text-sm text-gray-600">{classItem.teacher}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-blue-600">{classItem.time}</p>
                      <p className="text-sm text-gray-500">{classItem.room}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No upcoming classes</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Recent Grades</CardTitle>
                <CardDescription className="mt-1">Latest test results</CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="text-sm">
                <ArrowUpRight className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {stats?.recentGrades && stats.recentGrades.length > 0 ? (
                stats.recentGrades.slice(0, 3).map((grade, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div>
                      <p className="font-medium text-gray-900">{grade.testName}</p>
                      <p className="text-sm text-gray-600">{grade.date}</p>
                    </div>
                    <div className="text-right">
                      <p className={`text-sm font-medium ${grade.passed ? 'text-green-600' : 'text-red-600'}`}>
                        {grade.score}%
                      </p>
                      <p className="text-sm text-gray-500">
                        {grade.passed ? 'Passed' : 'Failed'}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Award className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No recent grades</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Section */}
      <Card className="dashboard-card">
        <CardHeader className="dashboard-card-header">
          <CardTitle className="text-lg font-semibold text-gray-900">Progress to Next Level</CardTitle>
          <CardDescription>Your advancement towards {stats?.nextLevel || 'next level'}</CardDescription>
        </CardHeader>
        <CardContent className="dashboard-card-content">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Overall Progress</span>
              <span className="text-sm font-medium text-blue-600">{stats?.progressToNext.percentage || 0}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                style={{ width: `${stats?.progressToNext.percentage || 0}%` }}
              ></div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Requirements:</h4>
                <ul className="space-y-1">
                  {stats?.progressToNext.requirements?.map((req, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      {req}
                    </li>
                  )) || (
                    <li className="text-sm text-gray-500">No requirements available</li>
                  )}
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
