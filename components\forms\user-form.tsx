'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Eye, EyeOff, User, Phone, Mail, Shield, Lock, AlertCircle, CheckCircle } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

const userSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phone: z.string().min(9, 'Phone number must be at least 9 characters'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  role: z.enum(['ADMIN', 'MANAGER', 'RECEPTION', 'CASHIER']),
  branch: z.enum(['main', 'branch']),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string().min(6, 'Please confirm your password'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don&apos;t match",
  path: ["confirmPassword"],
})

const userUpdateSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phone: z.string().min(9, 'Phone number must be at least 9 characters'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  role: z.enum(['ADMIN', 'MANAGER', 'RECEPTION', 'CASHIER']),
  branch: z.enum(['main', 'branch']),
  password: z.string().optional(),
  confirmPassword: z.string().optional(),
}).refine((data) => {
  if (data.password || data.confirmPassword) {
    return data.password === data.confirmPassword && (data.password?.length || 0) >= 6
  }
  return true
}, {
  message: "Passwords don&apos;t match or are too short",
  path: ["confirmPassword"],
})

interface User {
  id: string
  name: string
  phone: string
  email: string | null
  role: string
  branch: string
}

interface UserFormProps {
  user?: User | null
  onSuccess: () => void
  onCancel: () => void
}

const roleDescriptions = {
  ADMIN: 'Full system access including financial data and user management',
  MANAGER: 'Management access to operations and staff oversight',
  RECEPTION: 'Lead management, group coordination, and front desk operations',
  CASHIER: 'Payment processing and financial transaction management'
}

const roleColors = {
  ADMIN: 'text-red-600 bg-red-50 border-red-200',
  MANAGER: 'text-blue-600 bg-blue-50 border-blue-200',
  RECEPTION: 'text-purple-600 bg-purple-50 border-purple-200',
  CASHIER: 'text-orange-600 bg-orange-50 border-orange-200'
}

export function UserForm({ user, onSuccess, onCancel }: UserFormProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedRole, setSelectedRole] = useState<string>('')
  const { toast } = useToast()

  const isEditing = !!user

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm({
    resolver: zodResolver(isEditing ? userUpdateSchema : userSchema),
    defaultValues: {
      name: user?.name || '',
      phone: user?.phone || '',
      email: user?.email || '',
      role: user?.role || '',
      branch: user?.branch || 'main',
      password: '',
      confirmPassword: '',
    }
  })

  const watchedRole = watch('role')

  useEffect(() => {
    setSelectedRole(watchedRole)
  }, [watchedRole])

  useEffect(() => {
    if (user) {
      reset({
        name: user.name,
        phone: user.phone,
        email: user.email || '',
        role: user.role,
        branch: user.branch || 'main',
        password: '',
        confirmPassword: '',
      })
      setSelectedRole(user.role)
    }
  }, [user, reset])

  const onSubmit = async (data: any) => {
    setIsSubmitting(true)
    try {
      const payload = {
        name: data.name,
        phone: data.phone,
        email: data.email || null,
        role: data.role,
        branch: data.branch,
        ...(!isEditing && { password: data.password }), // Always include password for new users
        ...(isEditing && data.password && { password: data.password }), // Only include password for updates if provided
      }

      const url = isEditing ? '/api/users' : '/api/users'
      const method = isEditing ? 'PUT' : 'POST'
      
      if (isEditing) {
        payload.id = user!.id
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to save user')
      }

      toast({
        title: "Success",
        description: `User ${isEditing ? 'updated' : 'created'} successfully`,
      })

      onSuccess()
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : 'An error occurred',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Basic Information</span>
            </CardTitle>
            <CardDescription>
              Enter the user&apos;s personal details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Full Name *</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Enter full name"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="phone">Phone Number *</Label>
              <Input
                id="phone"
                {...register('phone')}
                placeholder="+998901234567"
                className={errors.phone ? 'border-red-500' : ''}
              />
              {errors.phone && (
                <p className="text-sm text-red-500 mt-1">{errors.phone.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                {...register('email')}
                placeholder="<EMAIL>"
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && (
                <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">Optional - used for notifications</p>
            </div>
          </CardContent>
        </Card>

        {/* Role & Security */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Role & Security</span>
            </CardTitle>
            <CardDescription>
              Set user role and access credentials
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="role">User Role *</Label>
              <Select onValueChange={(value) => setValue('role', value)} defaultValue={user?.role}>
                <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select user role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ADMIN">Admin</SelectItem>
                  <SelectItem value="MANAGER">Manager</SelectItem>
                  <SelectItem value="TEACHER">Teacher</SelectItem>
                  <SelectItem value="RECEPTION">Reception</SelectItem>
                  <SelectItem value="CASHIER">Cashier</SelectItem>
                  <SelectItem value="STUDENT">Student</SelectItem>
                  <SelectItem value="PARENT">Parent</SelectItem>
                </SelectContent>
              </Select>
              {errors.role && (
                <p className="text-sm text-red-500 mt-1">{errors.role.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="branch">Branch Assignment *</Label>
              <Select onValueChange={(value) => setValue('branch', value)} defaultValue={user?.branch || 'main'}>
                <SelectTrigger className={errors.branch ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select branch" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="main">Main Branch</SelectItem>
                  <SelectItem value="branch">Branch</SelectItem>
                </SelectContent>
              </Select>
              {errors.branch && (
                <p className="text-sm text-red-500 mt-1">{errors.branch.message}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">
                Users will only see data from their assigned branch (except ADMIN users who can switch)
              </p>
            </div>

            <div>
              <Label htmlFor="password">
                Password {isEditing ? '(leave blank to keep current)' : '*'}
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  {...register('password')}
                  placeholder={isEditing ? 'Enter new password' : 'Enter password'}
                  className={errors.password ? 'border-red-500' : ''}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-500 mt-1">{errors.password.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="confirmPassword">
                Confirm Password {isEditing ? '(if changing)' : '*'}
              </Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  {...register('confirmPassword')}
                  placeholder="Confirm password"
                  className={errors.confirmPassword ? 'border-red-500' : ''}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
              {errors.confirmPassword && (
                <p className="text-sm text-red-500 mt-1">{errors.confirmPassword.message}</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Role Description */}
      {selectedRole && (
        <Alert className={roleColors[selectedRole as keyof typeof roleColors]}>
          <Shield className="h-4 w-4" />
          <AlertDescription>
            <strong>{selectedRole} Role:</strong> {roleDescriptions[selectedRole as keyof typeof roleDescriptions]}
          </AlertDescription>
        </Alert>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4 pt-4 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {isEditing ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              {isEditing ? 'Update User' : 'Create User'}
            </>
          )}
        </Button>
      </div>
    </form>
  )
}
