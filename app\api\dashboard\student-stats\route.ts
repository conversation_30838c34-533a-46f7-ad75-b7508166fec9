import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is student
    if (session.user.role !== 'STUDENT') {
      return NextResponse.json({ error: 'Forbidden - Student access required' }, { status: 403 })
    }

    // Get student profile
    const student = await prisma.student.findUnique({
      where: { userId: session.user.id },
      include: {
        enrollments: {
          include: {
            group: {
              include: {
                course: true,
                teacher: {
                  include: {
                    user: { select: { name: true } }
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!student) {
      return NextResponse.json({ error: 'Student profile not found' }, { status: 404 })
    }

    const stats = {
      currentLevel: student.level || 'A1',
      nextLevel: getNextLevel(student.level || 'A1'),
      attendanceRate: {
        rate: 88, // Mock data
        trend: 2 // Mock data
      },
      averageScore: {
        score: 76, // Mock data
        trend: 4 // Mock data
      },
      paymentStatus: {
        status: 'PAID', // Mock data
        nextDue: 'Next month',
        amount: 500000 // Mock data
      },
      upcomingClasses: student.enrollments.slice(0, 3).map(enrollment => ({
        subject: enrollment.group?.course?.name || 'Unknown',
        teacher: enrollment.group?.teacher?.user?.name || 'Unknown',
        time: '10:00 AM', // Mock data
        room: 'Room 101' // Mock data
      })),
      recentGrades: [
        {
          testName: 'Unit 5 Test',
          score: 82,
          date: '1 week ago',
          passed: true
        },
        {
          testName: 'Speaking Assessment',
          score: 78,
          date: '2 weeks ago',
          passed: true
        }
      ],
      progressToNext: {
        percentage: 65,
        requirements: [
          'Complete 80% of assignments',
          'Pass final exam with 70%+',
          'Attend 90% of classes'
        ]
      }
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching student dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getNextLevel(currentLevel: string): string {
  const levels = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']
  const currentIndex = levels.indexOf(currentLevel)
  return currentIndex >= 0 && currentIndex < levels.length - 1 
    ? levels[currentIndex + 1] 
    : 'Advanced'
}
