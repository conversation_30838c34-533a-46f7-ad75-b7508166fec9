import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is cashier
    if (session.user.role !== 'CASHIER') {
      return NextResponse.json({ error: 'Forbidden - Cashier access required' }, { status: 403 })
    }

    // Get user's branch for filtering
    const userBranch = session.user.branch || 'main'

    const totalStudents = await prisma.student.count({
      where: { branch: userBranch }
    })

    // Get payment stats
    let todayPayments = { count: 0, amount: 0 }
    let pendingPayments = { count: 0, amount: 0 }
    let overduePayments = { count: 0, amount: 0 }
    let recentPayments: any[] = []

    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)

      // Today's payments - filtered by student branch
      const todayPaymentsData = await prisma.payment.aggregate({
        where: {
          status: 'PAID',
          createdAt: { gte: today, lt: tomorrow },
          student: { branch: userBranch }
        },
        _sum: { amount: true },
        _count: true
      })

      todayPayments = {
        count: todayPaymentsData._count || 0,
        amount: Number(todayPaymentsData._sum.amount) || 0
      }

      // Pending payments (debt status) - filtered by student branch
      const pendingPaymentsData = await prisma.payment.aggregate({
        where: {
          status: 'DEBT',
          student: { branch: userBranch }
        },
        _sum: { amount: true },
        _count: true
      })

      pendingPayments = {
        count: pendingPaymentsData._count || 0,
        amount: Number(pendingPaymentsData._sum.amount) || 0
      }

      // Recent payments - filtered by student branch
      recentPayments = await prisma.payment.findMany({
        where: {
          status: 'PAID',
          student: { branch: userBranch }
        },
        orderBy: { createdAt: 'desc' },
        take: 5,
        include: {
          student: {
            include: {
              user: { select: { name: true } }
            }
          }
        }
      })
    } catch (error) {
      console.log('Payments not available:', error)
    }

    const stats = {
      todayPayments,
      pendingPayments,
      overduePayments,
      totalStudents: { count: totalStudents },
      recentPayments: recentPayments.map(payment => ({
        studentName: payment.student?.user?.name || 'Unknown',
        amount: Number(payment.amount),
        method: payment.method || 'CASH',
        time: getTimeAgo(payment.createdAt),
        status: payment.status
      })),
      upcomingDues: [] // Placeholder
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching cashier dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }
}
