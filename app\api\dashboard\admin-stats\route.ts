import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 })
    }

    // Get current date ranges
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)

    // Get total students count
    const totalStudents = await prisma.student.count()
    const lastMonthStudents = await prisma.student.count({
      where: { createdAt: { lte: endOfLastMonth } }
    })
    const studentGrowth = lastMonthStudents > 0 
      ? Math.round(((totalStudents - lastMonthStudents) / lastMonthStudents) * 100)
      : 0

    // Get total users count
    const totalUsers = await prisma.user.count()

    // Get active groups count
    const activeGroups = await prisma.group.count({
      where: { isActive: true }
    })

    // Get monthly revenue
    let currentRevenue = 0
    let previousRevenue = 0
    try {
      const monthlyRevenue = await prisma.payment.aggregate({
        where: {
          status: 'PAID',
          createdAt: { gte: startOfMonth, lte: now }
        },
        _sum: { amount: true }
      })

      const lastMonthRevenue = await prisma.payment.aggregate({
        where: {
          status: 'PAID',
          createdAt: { gte: startOfLastMonth, lte: endOfLastMonth }
        },
        _sum: { amount: true }
      })

      currentRevenue = Number(monthlyRevenue._sum.amount) || 0
      previousRevenue = Number(lastMonthRevenue._sum.amount) || 0
    } catch (error) {
      console.log('Payments table not available:', error)
    }

    const revenueGrowth = previousRevenue > 0
      ? Math.round(((currentRevenue - previousRevenue) / previousRevenue) * 100)
      : 0

    // Get recent leads
    let recentLeads: any[] = []
    try {
      recentLeads = await prisma.lead.findMany({
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          id: true,
          name: true,
          coursePreference: true,
          createdAt: true
        }
      })
    } catch (error) {
      console.log('Recent leads not available:', error)
    }

    // Format the response
    const stats = {
      totalStudents: {
        count: totalStudents,
        growth: studentGrowth
      },
      totalUsers: {
        count: totalUsers
      },
      activeGroups: {
        count: activeGroups
      },
      monthlyRevenue: {
        amount: currentRevenue,
        growth: revenueGrowth
      },
      recentLeads: recentLeads.map(lead => ({
        name: lead.name,
        course: lead.coursePreference || 'Not specified',
        status: 'NEW',
        time: getTimeAgo(lead.createdAt)
      })),
      upcomingClasses: [], // Placeholder for now
      recentActivity: [] // Placeholder for now
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching admin dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }
}
