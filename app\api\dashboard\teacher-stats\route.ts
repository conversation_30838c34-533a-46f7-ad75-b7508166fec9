import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is teacher
    if (session.user.role !== 'TEACHER') {
      return NextResponse.json({ error: 'Forbidden - Teacher access required' }, { status: 403 })
    }

    // Get teacher profile
    const teacher = await prisma.teacher.findUnique({
      where: { userId: session.user.id },
      include: {
        groups: {
          where: { isActive: true },
          include: {
            currentStudents: true
          }
        }
      }
    })

    if (!teacher) {
      return NextResponse.json({ error: 'Teacher profile not found' }, { status: 404 })
    }

    // Calculate stats
    const myGroups = teacher.groups.length
    const myStudents = teacher.groups.reduce((total, group) => total + group.currentStudents.length, 0)
    const todayClasses = 1 // Mock data for now

    const stats = {
      myStudents: { count: myStudents },
      myGroups: { count: myGroups },
      todayClasses: { count: todayClasses },
      attendanceRate: { rate: 85, trend: 2 }, // Mock data
      myPerformance: {
        tier: teacher.tier || 'NEW',
        rating: 4.2, // Mock data
        completionRate: 92 // Mock data
      },
      upcomingClasses: teacher.groups.slice(0, 3).map(group => ({
        group: group.name,
        time: '10:00 AM', // Mock data
        room: 'Room 101', // Mock data
        studentCount: group.currentStudents.length
      })),
      recentAttendance: [] // Placeholder
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching teacher dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
