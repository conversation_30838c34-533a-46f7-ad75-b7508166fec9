"use client";

import * as React from "react";
import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";
import Link from "next/link";

import { SearchForm } from "@/components/search-form";
import { TeamSwitcher } from "@/components/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/sidebar";
import {
  LayoutDashboard,
  Users,
  UserPlus,
  GraduationCap,
  BookOpen,
  CreditCard,
  BarChart3,
  Settings,
  UserCheck,
  Calendar,
  ClipboardList,
  Award,
  Target,
  TrendingUp,
  FileText,
  MessageSquare,
  Shield,
  Activity,
  PieChart,
  ClipboardCheck,
  Building,
  LogOut,
} from "lucide-react";

// CRM Navigation Configuration
const crmNavigation = {
  teams: [
    {
      name: "Innovative Centre",
    },
  ],
  navMain: [
    {
      title: "Dashboard",
      url: "#",
      items: [
        {
          title: "Overview",
          url: "/dashboard",
          icon: LayoutDashboard,
          roles: ['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'STUDENT', 'ACADEMIC_MANAGER']
        },
      ],
    },
    {
      title: "Lead Management",
      url: "#",
      items: [
        {
          title: "Leads",
          url: "/dashboard/leads",
          icon: UserPlus,
          roles: ['ADMIN', 'MANAGER', 'RECEPTION']
        },
      ],
    },
    {
      title: "Academic Management",
      url: "#",
      items: [
        {
          title: "Cabinets",
          url: "/dashboard/cabinets",
          icon: Building,
          roles: ['ADMIN', 'MANAGER']
        },
      ],
    },
    {
      title: "Financial Management",
      url: "#",
      items: [
        {
          title: "Payments",
          url: "/dashboard/payments",
          icon: CreditCard,
          roles: ['ADMIN', 'CASHIER']
        },
        {
          title: "Analytics",
          url: "/dashboard/analytics",
          icon: BarChart3,
          roles: ['ADMIN']
        },
      ],
    },
    {
      title: "Student Progress",
      url: "#",
      items: [
        {
          title: "My Progress",
          url: "/dashboard/student/progress",
          icon: TrendingUp,
          roles: ['STUDENT']
        },
        {
          title: "My Assignments",
          url: "/dashboard/student/assignments",
          icon: ClipboardList,
          roles: ['STUDENT']
        },
        {
          title: "My Certificates",
          url: "/dashboard/student/certificates",
          icon: Award,
          roles: ['STUDENT']
        },
      ],
    },
    {
      title: "Communication",
      url: "#",
      items: [
        {
          title: "Messages",
          url: "/dashboard/communication",
          icon: MessageSquare,
          roles: ['ADMIN', 'MANAGER', 'RECEPTION']
        },
      ],
    },
    {
      title: "Administration",
      url: "#",
      items: [
        {
          title: "Users",
          url: "/dashboard/users",
          icon: Shield,
          roles: ['ADMIN']
        },
        {
          title: "Activity Logs",
          url: "/dashboard/admin/activity-logs",
          icon: Activity,
          roles: ['ADMIN']
        },
        {
          title: "KPIs",
          url: "/dashboard/admin/kpis",
          icon: PieChart,
          roles: ['ADMIN']
        },
        {
          title: "Settings",
          url: "/dashboard/settings",
          icon: Settings,
          roles: ['ADMIN', 'MANAGER']
        },
      ],
    },
  ],
};

// Helper function to check if user has access to a navigation item
function hasAccess(userRole: string, allowedRoles: string[]): boolean {
  return allowedRoles.includes(userRole);
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const userRole = session?.user?.role || 'STUDENT';

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={crmNavigation.teams} />
        <SearchForm />
      </SidebarHeader>
      <SidebarContent>
        {crmNavigation.navMain.map((section) => {
          // Filter items based on user role
          const accessibleItems = section.items?.filter(item =>
            hasAccess(userRole, item.roles)
          );

          // Only show section if user has access to at least one item
          if (!accessibleItems || accessibleItems.length === 0) {
            return null;
          }

          return (
            <SidebarGroup key={section.title}>
              <SidebarGroupLabel>{section.title}</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  {accessibleItems.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild isActive={pathname === item.url}>
                        <Link href={item.url}>
                          <item.icon />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          );
        })}
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton>
              <LogOut />
              <span>Sign out</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
