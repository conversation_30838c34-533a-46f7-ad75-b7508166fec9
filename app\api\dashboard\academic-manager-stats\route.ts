import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is academic manager
    if (session.user.role !== 'ACADEMIC_MANAGER') {
      return NextResponse.json({ error: 'Forbidden - Academic Manager access required' }, { status: 403 })
    }

    // Get user's branch for filtering
    const userBranch = session.user.branch || 'main'

    const totalStudents = await prisma.student.count({
      where: { branch: userBranch }
    })

    const stats = {
      totalStudents: {
        count: totalStudents,
        growth: 4 // Mock growth
      },
      totalAssessments: {
        count: 45, // Mock data
        thisMonth: 12 // Mock data
      },
      averageScore: {
        score: 78, // Mock data
        trend: 3 // Mock data
      },
      completionRate: {
        rate: 89, // Mock data
        trend: 2 // Mock data
      },
      recentAssessments: [
        {
          studentName: '<PERSON>e',
          testName: 'English Level Test',
          score: 85,
          date: '2 days ago',
          passed: true
        },
        {
          studentName: 'Jane Smith',
          testName: 'Math Assessment',
          score: 72,
          date: '3 days ago',
          passed: true
        }
      ],
      upcomingTests: [
        {
          testName: 'Final English Exam',
          groupName: 'Advanced English',
          date: 'Tomorrow',
          studentsCount: 15
        }
      ],
      performanceByLevel: [
        { level: 'A1', averageScore: 82, studentCount: 25 },
        { level: 'A2', averageScore: 78, studentCount: 30 },
        { level: 'B1', averageScore: 75, studentCount: 20 }
      ]
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching academic manager dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
