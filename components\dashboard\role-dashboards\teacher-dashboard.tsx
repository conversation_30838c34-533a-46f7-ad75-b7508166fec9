'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/card'
import { StatsGrid } from '@/components/stats-grid'
import {
  Users,
  GraduationCap,
  TrendingUp,
  Calendar,
  CheckCircle,
  ArrowUpRight,
  BookOpen,
  Loader2,
  User<PERSON>heck,
  ClipboardCheck,
  Award,
  Clock
} from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'
import { useDashboardStore } from '@/lib/stores/dashboard-store'
import { Button } from '@/components/button'
import Link from 'next/link'
import { useSession } from 'next-auth/react'

interface TeacherDashboardStats {
  myStudents: { count: number }
  myGroups: { count: number }
  todayClasses: { count: number }
  attendanceRate: { rate: number; trend: number }
  upcomingClasses: Array<{ group: string; time: string; room: string; studentCount: number }>
  recentAttendance: Array<{ groupName: string; date: string; present: number; total: number }>
  myPerformance: { tier: string; rating: number; completionRate: number }
}

export default function TeacherDashboard() {
  const { data: session } = useSession()
  const { currentBranch } = useBranch()
  const { refreshData } = useDashboardStore()
  const [stats, setStats] = useState<TeacherDashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchTeacherStats()
    refreshData()
  }, [])

  const fetchTeacherStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/dashboard/teacher-stats')
      if (!response.ok) {
        throw new Error('Failed to fetch teacher dashboard stats')
      }
      const data = await response.json()
      setStats(data)
      setError(null)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching teacher dashboard stats:', error)
      }
      setError('Failed to load teacher dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num)
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'A_LEVEL': return 'text-green-600'
      case 'B_LEVEL': return 'text-blue-600'
      case 'C_LEVEL': return 'text-orange-600'
      default: return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchTeacherStats}>Try Again</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 tracking-tight">
            <GraduationCap className="inline h-8 w-8 mr-3 text-blue-600" />
            Welcome, {session?.user?.name}
          </h1>
          <p className="text-gray-600 mt-1">Your teaching dashboard and class management tools.</p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            className="shadow-sm"
            onClick={() => {
              fetchTeacherStats()
              refreshData()
            }}
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            Refresh Data
          </Button>
          <Link href="/dashboard/attendance">
            <Button className="shadow-sm">
              <ClipboardCheck className="h-4 w-4 mr-2" />
              Take Attendance
            </Button>
          </Link>
        </div>
      </div>

      {/* Teacher Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">My Students</CardTitle>
              <div className="h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-blue-700">{formatNumber(stats?.myStudents.count || 0)}</div>
            <div className="kpi-change mt-2 text-blue-600">
              <Users className="h-4 w-4" />
              <span>Total students</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">My Groups</CardTitle>
              <div className="h-10 w-10 rounded-full bg-green-50 flex items-center justify-center">
                <BookOpen className="h-5 w-5 text-green-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-green-700">{formatNumber(stats?.myGroups.count || 0)}</div>
            <div className="kpi-change mt-2 text-green-600">
              <BookOpen className="h-4 w-4" />
              <span>Active groups</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Today&apos;s Classes</CardTitle>
              <div className="h-10 w-10 rounded-full bg-purple-50 flex items-center justify-center">
                <Calendar className="h-5 w-5 text-purple-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-purple-700">{formatNumber(stats?.todayClasses.count || 0)}</div>
            <div className="kpi-change mt-2 text-purple-600">
              <Calendar className="h-4 w-4" />
              <span>Scheduled today</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Teacher Tier</CardTitle>
              <div className="h-10 w-10 rounded-full bg-orange-50 flex items-center justify-center">
                <Award className="h-5 w-5 text-orange-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className={`kpi-value ${getTierColor(stats?.myPerformance.tier || '')}`}>
              {stats?.myPerformance.tier?.replace('_', '-') || 'NEW'}
            </div>
            <div className="kpi-change mt-2 text-orange-600">
              <Award className="h-4 w-4" />
              <span>Performance tier</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Teacher Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <CardTitle className="text-lg font-semibold text-gray-900">Quick Actions</CardTitle>
            <CardDescription>Teaching tools and resources</CardDescription>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-3">
              <Link href="/dashboard/groups">
                <Button variant="outline" className="w-full justify-start">
                  <BookOpen className="h-4 w-4 mr-2" />
                  My Groups
                </Button>
              </Link>
              <Link href="/dashboard/students">
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  My Students
                </Button>
              </Link>
              <Link href="/dashboard/attendance">
                <Button variant="outline" className="w-full justify-start">
                  <ClipboardCheck className="h-4 w-4 mr-2" />
                  Take Attendance
                </Button>
              </Link>
              <Link href="/dashboard/assessments">
                <Button variant="outline" className="w-full justify-start">
                  <Award className="h-4 w-4 mr-2" />
                  Assessments
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Today&apos;s Schedule</CardTitle>
                <CardDescription className="mt-1">Your upcoming classes</CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="text-sm">
                <Calendar className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {stats?.upcomingClasses && stats.upcomingClasses.length > 0 ? (
                stats.upcomingClasses.slice(0, 3).map((classItem, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div>
                      <p className="font-medium text-gray-900">{classItem.group}</p>
                      <p className="text-sm text-gray-600">{classItem.studentCount} students</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-blue-600">{classItem.time}</p>
                      <p className="text-sm text-gray-500">{classItem.room}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No classes today</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Recent Attendance</CardTitle>
                <CardDescription className="mt-1">Class attendance overview</CardDescription>
              </div>
              <Link href="/dashboard/attendance">
                <Button variant="ghost" size="sm" className="text-sm">
                  <ArrowUpRight className="h-4 w-4" />
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {stats?.recentAttendance && stats.recentAttendance.length > 0 ? (
                stats.recentAttendance.slice(0, 3).map((attendance, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div>
                      <p className="font-medium text-gray-900">{attendance.groupName}</p>
                      <p className="text-sm text-gray-600">{attendance.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-green-600">{attendance.present}/{attendance.total}</p>
                      <p className="text-sm text-gray-500">
                        {Math.round((attendance.present / attendance.total) * 100)}% present
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <ClipboardCheck className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No recent attendance</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
