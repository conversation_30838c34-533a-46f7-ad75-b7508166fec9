import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const range = searchParams.get('range') || '30days'

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    
    switch (range) {
      case '7days':
        startDate.setDate(now.getDate() - 7)
        break
      case '30days':
        startDate.setDate(now.getDate() - 30)
        break
      case '90days':
        startDate.setDate(now.getDate() - 90)
        break
      case '6months':
        startDate.setMonth(now.getMonth() - 6)
        break
      default:
        startDate.setDate(now.getDate() - 30)
    }

    // Generate date array for daily attendance
    const dates = []
    const current = new Date(startDate)
    while (current <= now) {
      dates.push(new Date(current))
      current.setDate(current.getDate() + 1)
    }

    // Get daily attendance data
    const dailyAttendance = []
    for (const date of dates) {
      const dayStart = new Date(date)
      dayStart.setHours(0, 0, 0, 0)
      const dayEnd = new Date(date)
      dayEnd.setHours(23, 59, 59, 999)

      const attendanceStats = await prisma.attendance.groupBy({
        by: ['status'],
        where: {
          class: {
            date: {
              gte: dayStart,
              lte: dayEnd,
            },
          },
        },
        _count: { status: true },
      })

      const stats = {
        present: 0,
        absent: 0,
        late: 0,
        excused: 0,
      }

      attendanceStats.forEach(stat => {
        stats[stat.status.toLowerCase() as keyof typeof stats] = stat._count?.status || 0
      })

      const total = stats.present + stats.absent + stats.late + stats.excused
      const rate = total > 0 ? Math.round(((stats.present + stats.late) / total) * 100 * 10) / 10 : 0

      dailyAttendance.push({
        date: date.toISOString().split('T')[0],
        present: stats.present,
        absent: stats.absent,
        late: stats.late,
        excused: stats.excused,
        total,
        rate,
      })
    }

    // Get group attendance statistics
    const groupAttendance = await prisma.group.findMany({
      where: { isActive: true },
      include: {
        course: { select: { name: true, level: true } },
        teacher: { include: { user: { select: { name: true } } } },
        enrollments: {
          where: { status: 'ACTIVE' },
          include: {
            student: {
              include: {
                attendances: {
                  where: {
                    class: {
                      date: {
                        gte: startDate,
                        lte: now,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    })

    const groupAttendanceData = groupAttendance.map(group => {
      const totalClasses = group.enrollments.reduce((sum, enrollment) => {
        return sum + enrollment.student.attendances.length
      }, 0)

      const presentClasses = group.enrollments.reduce((sum, enrollment) => {
        return sum + enrollment.student.attendances.filter(att => 
          att.status === 'PRESENT' || att.status === 'LATE'
        ).length
      }, 0)

      const attendanceRate = totalClasses > 0 ? Math.round((presentClasses / totalClasses) * 100 * 10) / 10 : 0
      const averageAttendance = group.enrollments.length > 0 ? Math.round(presentClasses / group.enrollments.length * 10) / 10 : 0

      return {
        group: group.name,
        course: group.course?.name || 'Unknown Course',
        level: group.course?.level || 'Unknown',
        teacher: group.teacher?.user?.name || 'No Teacher',
        attendanceRate,
        totalClasses,
        averageAttendance,
        activeStudents: group.enrollments.length,
      }
    })

    // Calculate overall statistics
    const totalAttendanceRecords = dailyAttendance.reduce((sum, day) => sum + day.total, 0)
    const totalPresentRecords = dailyAttendance.reduce((sum, day) => sum + day.present + day.late, 0)
    const overallAttendanceRate = totalAttendanceRecords > 0 
      ? Math.round((totalPresentRecords / totalAttendanceRecords) * 100 * 10) / 10 
      : 0

    // Get attendance trends
    const weeklyTrends = []
    for (let i = 0; i < dates.length; i += 7) {
      const weekDates = dates.slice(i, i + 7)
      const weekAttendance = dailyAttendance.slice(i, i + 7)
      
      const weekTotal = weekAttendance.reduce((sum, day) => sum + day.total, 0)
      const weekPresent = weekAttendance.reduce((sum, day) => sum + day.present + day.late, 0)
      const weekRate = weekTotal > 0 ? Math.round((weekPresent / weekTotal) * 100 * 10) / 10 : 0

      if (weekDates.length > 0) {
        weeklyTrends.push({
          week: `Week ${Math.floor(i / 7) + 1}`,
          startDate: weekDates[0].toISOString().split('T')[0],
          endDate: weekDates[weekDates.length - 1].toISOString().split('T')[0],
          attendanceRate: weekRate,
          totalClasses: weekTotal,
        })
      }
    }

    const response = {
      dailyAttendance,
      groupAttendance: groupAttendanceData,
      summary: {
        overallAttendanceRate,
        totalRecords: totalAttendanceRecords,
        totalPresentRecords,
        totalAbsentRecords: dailyAttendance.reduce((sum, day) => sum + day.absent, 0),
        totalLateRecords: dailyAttendance.reduce((sum, day) => sum + day.late, 0),
        totalExcusedRecords: dailyAttendance.reduce((sum, day) => sum + day.excused, 0),
        averageDailyAttendance: dates.length > 0 ? Math.round((totalAttendanceRecords / dates.length) * 10) / 10 : 0,
      },
      trends: {
        weekly: weeklyTrends,
      },
      dateRange: {
        start: startDate.toISOString(),
        end: now.toISOString(),
        range,
      },
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching attendance analytics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
