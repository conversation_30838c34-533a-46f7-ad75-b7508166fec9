import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is manager
    if (session.user.role !== 'MANAGER') {
      return NextResponse.json({ error: 'Forbidden - Manager access required' }, { status: 403 })
    }

    // Get user's branch for filtering
    const userBranch = session.user.branch || 'main'

    // Get basic stats (no financial data) - filtered by branch
    const totalStudents = await prisma.student.count({
      where: { branch: userBranch }
    })
    const totalTeachers = await prisma.teacher.count({
      where: { branch: userBranch }
    })
    const activeGroups = await prisma.group.count({
      where: {
        isActive: true,
        branch: userBranch
      }
    })
    const totalCabinets = await prisma.cabinet.count({
      where: { branch: userBranch }
    })
    const availableCabinets = await prisma.cabinet.count({
      where: {
        isActive: true,
        branch: userBranch
      }
    })

    // Get recent leads - filtered by branch
    let recentLeads: any[] = []
    try {
      recentLeads = await prisma.lead.findMany({
        where: { branch: userBranch },
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          name: true,
          coursePreference: true,
          createdAt: true
        }
      })
    } catch (error) {
      console.error('Error fetching recent leads:', error)
    }

    const stats = {
      totalStudents: {
        count: totalStudents,
        growth: 5 // Mock growth
      },
      totalTeachers: {
        count: totalTeachers
      },
      activeGroups: {
        count: activeGroups
      },
      totalCabinets: {
        count: totalCabinets,
        available: availableCabinets
      },
      recentLeads: recentLeads.map(lead => ({
        name: lead.name,
        course: lead.coursePreference || 'Not specified',
        status: 'NEW',
        time: getTimeAgo(lead.createdAt)
      })),
      upcomingClasses: [] // Placeholder
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching manager dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }
}
