import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is reception
    if (session.user.role !== 'RECEPTION') {
      return NextResponse.json({ error: 'Forbidden - Reception access required' }, { status: 403 })
    }

    // Get user's branch for filtering
    const userBranch = session.user.branch || 'main'

    // Get basic stats (no financial data) - filtered by branch
    const totalStudents = await prisma.student.count({
      where: { branch: userBranch }
    })
    const activeGroups = await prisma.group.count({
      where: {
        isActive: true,
        branch: userBranch
      }
    })

    // Get leads stats - filtered by branch
    let newLeads = 0
    let pendingLeads = 0
    let recentLeads: any[] = []
    try {
      const startOfWeek = new Date()
      startOfWeek.setDate(startOfWeek.getDate() - 7)

      newLeads = await prisma.lead.count({
        where: {
          createdAt: { gte: startOfWeek },
          branch: userBranch
        }
      })

      pendingLeads = await prisma.lead.count({
        where: {
          status: 'NEW',
          branch: userBranch
        }
      })

      recentLeads = await prisma.lead.findMany({
        where: {
          status: 'NEW',
          branch: userBranch
        },
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          name: true,
          coursePreference: true,
          phone: true,
          createdAt: true
        }
      })
    } catch (error) {
      console.error('Error fetching leads data:', error)
    }

    const stats = {
      totalStudents: {
        count: totalStudents,
        growth: 3 // Mock growth
      },
      newLeads: {
        count: newLeads,
        growth: 8 // Mock growth
      },
      activeGroups: {
        count: activeGroups
      },
      todayEnrollments: {
        count: 2 // Mock data
      },
      pendingLeads: {
        count: pendingLeads
      },
      recentLeads: recentLeads.map(lead => ({
        name: lead.name,
        course: lead.coursePreference || 'Not specified',
        phone: lead.phone,
        status: 'NEW',
        time: getTimeAgo(lead.createdAt)
      })),
      upcomingClasses: [] // Placeholder
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching reception dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }
}
